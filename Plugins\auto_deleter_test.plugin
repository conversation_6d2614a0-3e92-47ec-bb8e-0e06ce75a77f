import time
import threading
import traceback
from typing import Dict, Any, Optional
from dataclasses import dataclass
from java.util import ArrayList
from java.lang import Integer as jint

from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_messages_controller, get_user_config, run_on_ui_thread, get_last_fragment
from ui.settings import Header, Input, Switch, Divider, Text, Selector
from ui.bulletin import BulletinHelper
from android_utils import log
from org.telegram.tgnet import TLRPC

__id__ = "auto_deleter_test"
__name__ = "Auto Deleter Test"
__description__ = "Тестовая версия автоудаления с командой .deltest"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "msg_delete"

@dataclass
class PendingMessage:
    """Информация о сообщении, ожидающем удаления"""
    message_id: int
    chat_id: int
    topic_id: int
    timestamp: float
    timer: Optional[threading.Timer] = None

class AutoDeleterTestPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.pending_messages: Dict[str, PendingMessage] = {}
        self.lock = threading.Lock()
        self.test_enabled = False
        
    def on_plugin_load(self):
        """Инициализация плагина"""
        self.add_on_send_message_hook()
        self.add_hook("TL_updateNewMessage")
        self.log("Auto Deleter Test plugin loaded")
        
    def on_plugin_unload(self):
        """Очистка ресурсов при выгрузке плагина"""
        with self.lock:
            for pending_msg in self.pending_messages.values():
                if pending_msg.timer:
                    pending_msg.timer.cancel()
            self.pending_messages.clear()
        self.log("Auto Deleter Test plugin unloaded")
        
    def create_settings(self):
        """Создание интерфейса настроек"""
        return [
            Header(text="Тестовые настройки автоудаления"),
            Switch(
                key="enabled",
                text="Включить тестовый режим",
                default=False,
                subtext="Команда .deltest для тестирования удаления",
                icon="msg_delete"
            ),
            Selector(
                key="delete_time",
                text="Время до удаления",
                default=0,  # 5 секунд
                items=["5 секунд", "10 секунд", "30 секунд", "1 минута"],
                icon="msg_timer"
            ),
            Switch(
                key="delete_for_all",
                text="Удалять для всех",
                default=True,
                subtext="Удалять сообщения для всех участников чата",
                icon="msg_delete"
            ),
            Text(
                text=f"Активных таймеров: {len(self.pending_messages)}",
                icon="msg_stats"
            ),
            Divider(text="Отправьте .deltest чтобы протестировать автоудаление")
        ]
        
    def _get_delete_delay(self) -> float:
        """Получение времени задержки до удаления в секундах"""
        time_index = self.get_setting("delete_time", 0)
        time_values = [5, 10, 30, 60]  # в секундах
        return float(time_values[min(time_index, len(time_values) - 1)])
        
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Перехват отправки сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()
            
        try:
            # Проверяем, содержит ли сообщение команду .deltest
            message_text = ""
            if hasattr(params, 'message') and params.message:
                message_text = params.message
            elif hasattr(params, 'text') and params.text:
                message_text = params.text
                
            if ".deltest" in message_text.lower():
                self.test_enabled = True
                self.log("Test command detected, enabling auto-deletion for next message")
                
        except Exception as e:
            self.log(f"Error in on_send_message_hook: {e}")
            
        return HookResult()
        
    def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult:
        """Перехват обновлений о новых сообщениях"""
        if not self.get_setting("enabled", False):
            return HookResult()
            
        try:
            if update_name == "TL_updateNewMessage" and self.test_enabled:
                self.log(f"Processing TL_updateNewMessage update")
                
                if hasattr(update, 'message') and update.message:
                    message = update.message
                    
                    # Проверяем, что это наше сообщение
                    current_user_id = get_user_config().getClientUserId()
                    
                    if (hasattr(message, 'from_id') and 
                        hasattr(message.from_id, 'user_id') and 
                        message.from_id.user_id == current_user_id):
                        
                        # Проверяем, что это команда .deltest
                        message_text = getattr(message, 'message', '')
                        if ".deltest" in message_text.lower():
                            self.log(f"Found test command message with ID: {message.id}")
                            self._schedule_message_deletion(message)
                            self.test_enabled = False  # Отключаем после обработки
                        
        except Exception as e:
            self.log(f"Error in on_update_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
        return HookResult()
        
    def _schedule_message_deletion(self, message: Any):
        """Планирование удаления сообщения"""
        try:
            message_id = message.id
            chat_id = self._get_chat_id_from_message(message)
            topic_id = 0
            
            # Пытаемся получить topic_id из сообщения
            if hasattr(message, 'reply_to') and message.reply_to:
                if hasattr(message.reply_to, 'reply_to_top_id'):
                    topic_id = message.reply_to.reply_to_top_id
                    
            self.log(f"Scheduling deletion: msg_id={message_id}, chat_id={chat_id}, topic_id={topic_id}")
            
            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time()
            )
            
            # Планируем удаление
            delay = self._get_delete_delay()
            timer = threading.Timer(delay, self._delete_message, args=[pending_msg])
            pending_msg.timer = timer
            
            # Сохраняем в список ожидающих
            key = f"{chat_id}_{message_id}"
            with self.lock:
                self.pending_messages[key] = pending_msg
                
            timer.start()
            self.log(f"Timer started for message {message_id}, delay: {delay} seconds")
            
            run_on_ui_thread(lambda: BulletinHelper.show_info(
                f"Тестовое сообщение будет удалено через {int(delay)} сек."
            ))
                
        except Exception as e:
            self.log(f"Error scheduling message deletion: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
    def _delete_message(self, pending_msg: PendingMessage):
        """Удаление сообщения"""
        try:
            self.log(f"Attempting to delete message {pending_msg.message_id} in chat {pending_msg.chat_id}")
            
            # Удаляем из списка ожидающих
            key = f"{pending_msg.chat_id}_{pending_msg.message_id}"
            with self.lock:
                self.pending_messages.pop(key, None)
                
            # Создаем список ID сообщений для удаления
            msgs_list = ArrayList()
            msgs_list.add(jint(pending_msg.message_id))
            
            # Удаляем сообщение
            delete_for_all = self.get_setting("delete_for_all", True)
            
            self.log(f"Calling deleteMessages with: msgs={[pending_msg.message_id]}, "
                    f"chat_id={pending_msg.chat_id}, topic_id={pending_msg.topic_id}, "
                    f"delete_for_all={delete_for_all}")
            
            get_messages_controller().deleteMessages(
                msgs_list, None, None, 
                pending_msg.chat_id, pending_msg.topic_id, 
                delete_for_all, 0
            )
            
            run_on_ui_thread(lambda: BulletinHelper.show_success("Тестовое сообщение удалено"))
            self.log(f"Successfully deleted message {pending_msg.message_id}")
            
        except Exception as e:
            self.log(f"Error deleting message: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка удаления сообщения"))
            
    def _get_chat_id_from_message(self, message: Any) -> int:
        """Получение ID чата из объекта сообщения"""
        try:
            if hasattr(message, 'peer_id'):
                peer = message.peer_id
                if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                    return -peer.channel_id
                elif hasattr(peer, 'chat_id') and peer.chat_id != 0:
                    return -peer.chat_id
                elif hasattr(peer, 'user_id') and peer.user_id != 0:
                    return peer.user_id
        except Exception as e:
            self.log(f"Error getting chat ID from message: {e}")
        return 0
