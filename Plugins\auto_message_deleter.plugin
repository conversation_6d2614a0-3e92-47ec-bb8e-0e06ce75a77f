import time
import threading
import traceback
from typing import Dict, Any, Optional
from dataclasses import dataclass
from java.util import ArrayList
from java.lang import Integer as jint

from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_messages_controller, get_user_config, run_on_ui_thread
from ui.settings import Header, Input, Switch, Divider, Text, Selector
from ui.bulletin import BulletinHelper
from android_utils import log
from org.telegram.tgnet import TLRPC

__id__ = "auto_message_deleter"
__name__ = "Auto Message Deleter"
__description__ = "Автоматически удаляет отправленные сообщения через настраиваемое время"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "msg_delete"

@dataclass
class PendingMessage:
    """Информация о сообщении, ожидающем удаления"""
    message_id: int
    chat_id: int
    topic_id: int
    timestamp: float
    timer: Optional[threading.Timer] = None

class AutoMessageDeleterPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.pending_messages: Dict[str, PendingMessage] = {}
        self.pending_sends: Dict[int, Dict[str, Any]] = {}  # random_id -> send_params
        self.lock = threading.Lock()
        
    def on_plugin_load(self):
        """Инициализация плагина"""
        self.add_on_send_message_hook()
        self.add_hook("TL_messages_sendMessage")
        self.add_hook("TL_messages_sendMedia") 
        self.add_hook("TL_messages_sendInlineBotResult")
        self.add_hook("TL_messages_forwardMessages")
        self.log("Auto Message Deleter plugin loaded")
        
    def on_plugin_unload(self):
        """Очистка ресурсов при выгрузке плагина"""
        with self.lock:
            # Отменяем все активные таймеры
            for pending_msg in self.pending_messages.values():
                if pending_msg.timer:
                    pending_msg.timer.cancel()
            self.pending_messages.clear()
            self.pending_sends.clear()
        self.log("Auto Message Deleter plugin unloaded")
        
    def create_settings(self):
        """Создание интерфейса настроек"""
        return [
            Header(text="Настройки автоудаления"),
            Switch(
                key="enabled",
                text="Включить автоудаление",
                default=False,
                subtext="Автоматически удалять отправленные сообщения",
                icon="msg_delete"
            ),
            Selector(
                key="delete_time",
                text="Время до удаления",
                default=2,  # 30 секунд
                items=["5 секунд", "10 секунд", "30 секунд", "1 минута", "2 минуты", "5 минут", "10 минут"],
                icon="msg_timer"
            ),
            Switch(
                key="delete_for_all",
                text="Удалять для всех",
                default=True,
                subtext="Удалять сообщения для всех участников чата",
                icon="msg_delete"
            ),
            Switch(
                key="show_notifications",
                text="Показывать уведомления",
                default=True,
                subtext="Показывать уведомления об удалении сообщений",
                icon="msg_info"
            ),
            Divider(),
            Header(text="Статистика"),
            Text(
                text=f"Активных таймеров: {len(self.pending_messages)}",
                icon="msg_stats"
            ),
            Text(
                text="Очистить все таймеры",
                icon="msg_clear",
                red=True,
                on_click=self._clear_all_timers
            ),
            Divider(text="Плагин автоматически удаляет ваши отправленные сообщения через указанное время")
        ]
        
    def _clear_all_timers(self, view):
        """Очистка всех активных таймеров"""
        with self.lock:
            count = len(self.pending_messages)
            for pending_msg in self.pending_messages.values():
                if pending_msg.timer:
                    pending_msg.timer.cancel()
            self.pending_messages.clear()
            
        if count > 0:
            run_on_ui_thread(lambda: BulletinHelper.show_success(f"Отменено {count} таймеров удаления"))
        else:
            run_on_ui_thread(lambda: BulletinHelper.show_info("Нет активных таймеров"))
            
    def _get_delete_delay(self) -> float:
        """Получение времени задержки до удаления в секундах"""
        time_index = self.get_setting("delete_time", 2)
        time_values = [5, 10, 30, 60, 120, 300, 600]  # в секундах
        return float(time_values[min(time_index, len(time_values) - 1)])
        
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Перехват отправки сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()
            
        try:
            # Сохраняем параметры отправки для последующего сопоставления
            if hasattr(params, 'random_id') and params.random_id:
                with self.lock:
                    self.pending_sends[params.random_id] = {
                        'peer': params.peer,
                        'timestamp': time.time(),
                        'topic_id': self._get_topic_id(params)
                    }
                    
        except Exception as e:
            self.log(f"Error in on_send_message_hook: {e}")
            
        return HookResult()
        
    def post_request_hook(self, request_name: str, account: int, response: Any, error: Any) -> HookResult:
        """Перехват ответов на отправку сообщений"""
        if not self.get_setting("enabled", False) or error:
            return HookResult()
            
        try:
            if request_name in ["TL_messages_sendMessage", "TL_messages_sendMedia", 
                              "TL_messages_sendInlineBotResult", "TL_messages_forwardMessages"]:
                self._process_send_response(response)
                
        except Exception as e:
            self.log(f"Error in post_request_hook: {e}")
            
        return HookResult()
        
    def _process_send_response(self, response: Any):
        """Обработка ответа на отправку сообщения"""
        if not response:
            return
            
        try:
            message_id = None
            random_id = None
            
            # Извлекаем ID сообщения из ответа
            if hasattr(response, 'updates') and response.updates:
                # TLRPC.Updates с массивом обновлений
                for i in range(response.updates.size()):
                    update = response.updates.get(i)
                    if hasattr(update, '__class__'):
                        class_name = update.__class__.__name__
                        
                        if class_name == "TL_updateNewMessage":
                            if hasattr(update, 'message') and update.message:
                                message_id = update.message.id
                                random_id = getattr(update.message, 'random_id', None)
                                break
                        elif class_name == "TL_updateMessageID":
                            message_id = update.id
                            random_id = getattr(update, 'random_id', None)
                            break
                            
            elif hasattr(response, 'update'):
                # Одиночное обновление
                update = response.update
                if hasattr(update, '__class__'):
                    class_name = update.__class__.__name__
                    
                    if class_name == "TL_updateNewMessage":
                        if hasattr(update, 'message') and update.message:
                            message_id = update.message.id
                            random_id = getattr(update.message, 'random_id', None)
                    elif class_name == "TL_updateMessageID":
                        message_id = update.id
                        random_id = getattr(update, 'random_id', None)
                        
            if message_id and random_id:
                self._schedule_message_deletion(message_id, random_id)
                
        except Exception as e:
            self.log(f"Error processing send response: {e}")
            
    def _schedule_message_deletion(self, message_id: int, random_id: int):
        """Планирование удаления сообщения"""
        try:
            with self.lock:
                # Находим соответствующие параметры отправки
                send_params = self.pending_sends.get(random_id)
                if not send_params:
                    return
                    
                # Удаляем из pending_sends
                del self.pending_sends[random_id]
                
            # Получаем параметры чата
            chat_id = self._get_chat_id_from_peer(send_params['peer'])
            topic_id = send_params.get('topic_id', 0)
            
            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time()
            )
            
            # Планируем удаление
            delay = self._get_delete_delay()
            timer = threading.Timer(delay, self._delete_message, args=[pending_msg])
            pending_msg.timer = timer
            
            # Сохраняем в список ожидающих
            key = f"{chat_id}_{message_id}"
            with self.lock:
                self.pending_messages[key] = pending_msg
                
            timer.start()
            
            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_info(
                    f"Сообщение будет удалено через {int(delay)} сек."
                ))
                
        except Exception as e:
            self.log(f"Error scheduling message deletion: {e}")
            
    def _delete_message(self, pending_msg: PendingMessage):
        """Удаление сообщения"""
        try:
            # Удаляем из списка ожидающих
            key = f"{pending_msg.chat_id}_{pending_msg.message_id}"
            with self.lock:
                self.pending_messages.pop(key, None)
                
            # Создаем список ID сообщений для удаления
            msgs_list = ArrayList()
            msgs_list.add(jint(pending_msg.message_id))
            
            # Удаляем сообщение
            delete_for_all = self.get_setting("delete_for_all", True)
            get_messages_controller().deleteMessages(
                msgs_list, None, None, 
                pending_msg.chat_id, pending_msg.topic_id, 
                delete_for_all, 0
            )
            
            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_success("Сообщение удалено"))
                
            self.log(f"Deleted message {pending_msg.message_id} in chat {pending_msg.chat_id}")
            
        except Exception as e:
            self.log(f"Error deleting message: {e}")
            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка удаления сообщения"))
                
    def _get_chat_id_from_peer(self, peer: Any) -> int:
        """Получение ID чата из peer объекта"""
        try:
            if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                return -peer.channel_id
            elif hasattr(peer, 'chat_id') and peer.chat_id != 0:
                return -peer.chat_id
            elif hasattr(peer, 'user_id') and peer.user_id != 0:
                return peer.user_id
        except Exception as e:
            self.log(f"Error getting chat ID from peer: {e}")
        return 0
        
    def _get_topic_id(self, params: Any) -> int:
        """Получение ID топика из параметров сообщения"""
        try:
            if hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                if hasattr(params.replyToTopMsg, 'messageOwner'):
                    return getattr(params.replyToTopMsg.messageOwner, 'id', 0)
                return getattr(params.replyToTopMsg, 'id', 0)
        except Exception as e:
            self.log(f"Error getting topic ID: {e}")
        return 0
