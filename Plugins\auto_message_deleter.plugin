import time
import threading
import traceback
from typing import Dict, Any, Optional
from dataclasses import dataclass
from java.util import ArrayList
from java.lang import Integer as jint

from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_messages_controller, get_user_config, run_on_ui_thread
from ui.settings import Header, Input, Switch, Divider, Text, Selector
from ui.bulletin import BulletinHelper
from android_utils import log
from org.telegram.tgnet import TLRPC

__id__ = "auto_message_deleter"
__name__ = "Auto Message Deleter"
__description__ = "Автоматически удаляет отправленные сообщения через настраиваемое время"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "msg_delete"

@dataclass
class PendingMessage:
    """Информация о сообщении, ожидающем удаления"""
    message_id: int
    chat_id: int
    topic_id: int
    timestamp: float
    timer: Optional[threading.Timer] = None

class AutoMessageDeleterPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.pending_messages: Dict[str, PendingMessage] = {}
        self.pending_sends: Dict[int, Dict[str, Any]] = {}  # random_id -> send_params
        self.lock = threading.Lock()
        self.last_sent_messages = []  # Список последних отправленных сообщений для fallback
        
    def on_plugin_load(self):
        """Инициализация плагина"""
        self.add_on_send_message_hook()
        # Добавляем хуки для перехвата ответов на отправку сообщений
        self.add_hook("TL_messages_sendMessage")
        self.add_hook("TL_messages_sendMedia")
        self.add_hook("TL_messages_sendInlineBotResult")
        self.add_hook("TL_messages_forwardMessages")
        self.add_hook("TL_messages_sendMultiMedia")
        # Добавляем хук для перехвата обновлений о новых сообщениях
        self.add_hook("TL_updateNewMessage")
        self.log("Auto Message Deleter plugin loaded")
        
    def on_plugin_unload(self):
        """Очистка ресурсов при выгрузке плагина"""
        with self.lock:
            # Отменяем все активные таймеры
            for pending_msg in self.pending_messages.values():
                if pending_msg.timer:
                    pending_msg.timer.cancel()
            self.pending_messages.clear()
            self.pending_sends.clear()
        self.log("Auto Message Deleter plugin unloaded")
        
    def create_settings(self):
        """Создание интерфейса настроек"""
        return [
            Header(text="Настройки автоудаления"),
            Switch(
                key="enabled",
                text="Включить автоудаление",
                default=False,
                subtext="Автоматически удалять отправленные сообщения",
                icon="msg_delete"
            ),
            Selector(
                key="delete_time",
                text="Время до удаления",
                default=2,  # 30 секунд
                items=["5 секунд", "10 секунд", "30 секунд", "1 минута", "2 минуты", "5 минут", "10 минут"],
                icon="msg_timer"
            ),
            Switch(
                key="delete_for_all",
                text="Удалять для всех",
                default=True,
                subtext="Удалять сообщения для всех участников чата",
                icon="msg_delete"
            ),
            Switch(
                key="show_notifications",
                text="Показывать уведомления",
                default=True,
                subtext="Показывать уведомления об удалении сообщений",
                icon="msg_info"
            ),
            Divider(),
            Header(text="Статистика"),
            Text(
                text=f"Активных таймеров: {len(self.pending_messages)}",
                icon="msg_stats"
            ),
            Text(
                text="Очистить все таймеры",
                icon="msg_clear",
                red=True,
                on_click=self._clear_all_timers
            ),
            Divider(text="Плагин автоматически удаляет ваши отправленные сообщения через указанное время")
        ]
        
    def _clear_all_timers(self, view):
        """Очистка всех активных таймеров"""
        with self.lock:
            count = len(self.pending_messages)
            for pending_msg in self.pending_messages.values():
                if pending_msg.timer:
                    pending_msg.timer.cancel()
            self.pending_messages.clear()
            
        if count > 0:
            run_on_ui_thread(lambda: BulletinHelper.show_success(f"Отменено {count} таймеров удаления"))
        else:
            run_on_ui_thread(lambda: BulletinHelper.show_info("Нет активных таймеров"))
            
    def _get_delete_delay(self) -> float:
        """Получение времени задержки до удаления в секундах"""
        time_index = self.get_setting("delete_time", 2)
        time_values = [5, 10, 30, 60, 120, 300, 600]  # в секундах
        return float(time_values[min(time_index, len(time_values) - 1)])
        
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Перехват отправки сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()

        try:
            self.log(f"on_send_message_hook called with params: {type(params)}")

            # Генерируем уникальный ID для сопоставления с ответом
            send_timestamp = time.time()

            # Сохраняем параметры отправки
            with self.lock:
                # Используем timestamp как ключ, так как random_id может быть недоступен
                key = f"{send_timestamp}_{id(params)}"
                self.pending_sends[key] = {
                    'peer': getattr(params, 'peer', None),
                    'timestamp': send_timestamp,
                    'topic_id': self._get_topic_id(params),
                    'params_id': id(params)
                }

            self.log(f"Saved send params with key: {key}")

        except Exception as e:
            self.log(f"Error in on_send_message_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()
        
    def post_request_hook(self, request_name: str, account: int, response: Any, error: Any) -> HookResult:
        """Перехват ответов на отправку сообщений"""
        if not self.get_setting("enabled", False):
            return HookResult()

        if error:
            self.log(f"Request {request_name} failed with error: {error}")
            return HookResult()

        try:
            self.log(f"post_request_hook called: {request_name}, response type: {type(response)}")

            if request_name in ["TL_messages_sendMessage", "TL_messages_sendMedia",
                              "TL_messages_sendInlineBotResult", "TL_messages_forwardMessages",
                              "TL_messages_sendMultiMedia"]:
                self._process_send_response(response, request_name)

        except Exception as e:
            self.log(f"Error in post_request_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()

    def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult:
        """Перехват обновлений о новых сообщениях"""
        if not self.get_setting("enabled", False):
            return HookResult()

        try:
            if update_name == "TL_updateNewMessage":
                self.log(f"Received TL_updateNewMessage update")

                if hasattr(update, 'message') and update.message:
                    message = update.message

                    # Проверяем, что это наше сообщение
                    current_user_id = get_user_config().getClientUserId()

                    if (hasattr(message, 'from_id') and
                        hasattr(message.from_id, 'user_id') and
                        message.from_id.user_id == current_user_id):

                        self.log(f"Found our message with ID: {message.id}")

                        # Добавляем в список последних отправленных сообщений
                        with self.lock:
                            self.last_sent_messages.append({
                                'id': message.id,
                                'timestamp': time.time(),
                                'chat_id': self._get_chat_id_from_message(message)
                            })

                            # Оставляем только последние 10 сообщений
                            if len(self.last_sent_messages) > 10:
                                self.last_sent_messages = self.last_sent_messages[-10:]

                        # Планируем удаление
                        self._schedule_message_deletion_from_update(message)

        except Exception as e:
            self.log(f"Error in on_update_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

        return HookResult()

    def _get_chat_id_from_message(self, message: Any) -> int:
        """Получение ID чата из объекта сообщения"""
        try:
            if hasattr(message, 'peer_id'):
                peer = message.peer_id
                if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                    return -peer.channel_id
                elif hasattr(peer, 'chat_id') and peer.chat_id != 0:
                    return -peer.chat_id
                elif hasattr(peer, 'user_id') and peer.user_id != 0:
                    return peer.user_id
        except Exception as e:
            self.log(f"Error getting chat ID from message: {e}")
        return 0

    def _schedule_message_deletion_from_update(self, message: Any):
        """Планирование удаления сообщения из обновления"""
        try:
            message_id = message.id
            chat_id = self._get_chat_id_from_message(message)
            topic_id = 0

            # Пытаемся получить topic_id из сообщения
            if hasattr(message, 'reply_to') and message.reply_to:
                if hasattr(message.reply_to, 'reply_to_top_id'):
                    topic_id = message.reply_to.reply_to_top_id

            self.log(f"Scheduling deletion from update: msg_id={message_id}, chat_id={chat_id}, topic_id={topic_id}")

            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time()
            )

            # Планируем удаление
            delay = self._get_delete_delay()
            timer = threading.Timer(delay, self._delete_message, args=[pending_msg])
            pending_msg.timer = timer

            # Сохраняем в список ожидающих
            key = f"{chat_id}_{message_id}"
            with self.lock:
                self.pending_messages[key] = pending_msg

            timer.start()
            self.log(f"Timer started for message {message_id}, delay: {delay} seconds")

            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_info(
                    f"Сообщение будет удалено через {int(delay)} сек."
                ))

        except Exception as e:
            self.log(f"Error scheduling message deletion from update: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")

    def _process_send_response(self, response: Any, request_name: str):
        """Обработка ответа на отправку сообщения"""
        if not response:
            self.log("No response received")
            return

        try:
            self.log(f"Processing response for {request_name}: {type(response)}")
            response_class = response.__class__.__name__
            self.log(f"Response class name: {response_class}")
            message_ids = []

            # Обрабатываем TL_updateShortSentMessage
            if response_class == "TL_updateShortSentMessage":
                self.log("Processing TL_updateShortSentMessage")
                if hasattr(response, 'id'):
                    msg_id = response.id
                    message_ids.append(msg_id)
                    self.log(f"Found message ID from TL_updateShortSentMessage: {msg_id}")

            # Извлекаем ID сообщения из ответа с updates
            elif hasattr(response, 'updates') and response.updates:
                self.log(f"Found updates array with {response.updates.size()} items")
                # TLRPC.Updates с массивом обновлений
                for i in range(response.updates.size()):
                    update = response.updates.get(i)
                    class_name = update.__class__.__name__
                    self.log(f"Processing update {i}: {class_name}")

                    if class_name == "TL_updateNewMessage":
                        self.log(f"Processing TL_updateNewMessage, has message: {hasattr(update, 'message')}")
                        if hasattr(update, 'message') and update.message:
                            msg_id = update.message.id
                            message_ids.append(msg_id)
                            self.log(f"Found message ID from TL_updateNewMessage: {msg_id}")
                        else:
                            self.log("TL_updateNewMessage has no message field")
                    elif class_name == "TL_updateMessageID":
                        self.log(f"Processing TL_updateMessageID, has id: {hasattr(update, 'id')}")
                        if hasattr(update, 'id'):
                            msg_id = update.id
                            message_ids.append(msg_id)
                            self.log(f"Found message ID from TL_updateMessageID: {msg_id}")
                        else:
                            self.log("TL_updateMessageID has no id field")

            elif hasattr(response, 'update'):
                # Одиночное обновление
                update = response.update
                class_name = update.__class__.__name__
                self.log(f"Processing single update: {class_name}")

                if class_name == "TL_updateNewMessage":
                    if hasattr(update, 'message') and update.message:
                        msg_id = update.message.id
                        message_ids.append(msg_id)
                        self.log(f"Found message ID from single TL_updateNewMessage: {msg_id}")
                elif class_name == "TL_updateMessageID":
                    msg_id = update.id
                    message_ids.append(msg_id)
                    self.log(f"Found message ID from single TL_updateMessageID: {msg_id}")

            # Планируем удаление для всех найденных сообщений
            self.log(f"Total message IDs found: {len(message_ids)}")
            for message_id in message_ids:
                self.log(f"Scheduling deletion for message ID: {message_id}")
                self._schedule_message_deletion_simple(message_id)

        except Exception as e:
            self.log(f"Error processing send response: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
    def _schedule_message_deletion_simple(self, message_id: int):
        """Упрощенное планирование удаления сообщения"""
        try:
            self.log(f"Scheduling deletion for message ID: {message_id}")

            # Получаем текущий фрагмент для определения чата
            chat_id = 0
            topic_id = 0

            try:
                from client_utils import get_last_fragment
                fragment = get_last_fragment()
                if fragment:
                    self.log(f"Fragment type: {type(fragment)}")

                    # Пробуем разные способы получения dialog_id
                    if hasattr(fragment, 'getDialogId'):
                        chat_id = fragment.getDialogId()
                        self.log(f"Got chat_id from getDialogId(): {chat_id}")
                    elif hasattr(fragment, 'dialog_id'):
                        chat_id = fragment.dialog_id
                        self.log(f"Got chat_id from dialog_id: {chat_id}")
                    elif hasattr(fragment, 'currentAccount') and hasattr(fragment, 'dialog_id'):
                        chat_id = fragment.dialog_id
                        self.log(f"Got chat_id from dialog_id (alt): {chat_id}")

                    # Пробуем получить topic_id
                    if hasattr(fragment, 'threadMessageId'):
                        topic_id = getattr(fragment, 'threadMessageId', 0)
                        self.log(f"Got topic_id: {topic_id}")
                else:
                    self.log("No fragment available")
            except Exception as fragment_error:
                self.log(f"Error getting fragment: {fragment_error}")

            # Если не удалось получить chat_id из фрагмента, пробуем альтернативные способы
            if chat_id == 0:
                self.log("Trying alternative methods to get chat_id")

                # Пробуем получить из последних отправленных сообщений
                with self.lock:
                    if self.last_sent_messages:
                        latest_msg = self.last_sent_messages[-1]
                        if time.time() - latest_msg['timestamp'] < 5:  # Если сообщение отправлено недавно
                            chat_id = latest_msg['chat_id']
                            self.log(f"Got chat_id from last sent messages: {chat_id}")

            self.log(f"Final Chat ID: {chat_id}, Topic ID: {topic_id}")

            if chat_id == 0:
                self.log("Could not determine chat ID, cannot schedule deletion")
                return

            # Создаем объект ожидающего сообщения
            pending_msg = PendingMessage(
                message_id=message_id,
                chat_id=chat_id,
                topic_id=topic_id,
                timestamp=time.time()
            )

            # Планируем удаление
            delay = self._get_delete_delay()
            timer = threading.Timer(delay, self._delete_message, args=[pending_msg])
            pending_msg.timer = timer

            # Сохраняем в список ожидающих
            key = f"{chat_id}_{message_id}"
            with self.lock:
                self.pending_messages[key] = pending_msg

            timer.start()
            self.log(f"Timer started for message {message_id}, delay: {delay} seconds")

            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_info(
                    f"Сообщение будет удалено через {int(delay)} сек."
                ))

        except Exception as e:
            self.log(f"Error scheduling message deletion: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
    def _delete_message(self, pending_msg: PendingMessage):
        """Удаление сообщения"""
        try:
            self.log(f"Attempting to delete message {pending_msg.message_id} in chat {pending_msg.chat_id}")

            # Удаляем из списка ожидающих
            key = f"{pending_msg.chat_id}_{pending_msg.message_id}"
            with self.lock:
                self.pending_messages.pop(key, None)

            # Создаем список ID сообщений для удаления
            msgs_list = ArrayList()
            msgs_list.add(jint(pending_msg.message_id))

            # Удаляем сообщение
            delete_for_all = self.get_setting("delete_for_all", True)

            self.log(f"Calling deleteMessages with: msgs={[pending_msg.message_id]}, "
                    f"chat_id={pending_msg.chat_id}, topic_id={pending_msg.topic_id}, "
                    f"delete_for_all={delete_for_all}")

            get_messages_controller().deleteMessages(
                msgs_list, None, None,
                pending_msg.chat_id, pending_msg.topic_id,
                delete_for_all, 0
            )

            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_success("Сообщение удалено"))

            self.log(f"Successfully deleted message {pending_msg.message_id} in chat {pending_msg.chat_id}")

        except Exception as e:
            self.log(f"Error deleting message: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            if self.get_setting("show_notifications", True):
                run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка удаления сообщения"))
                
    def _get_chat_id_from_peer(self, peer: Any) -> int:
        """Получение ID чата из peer объекта"""
        try:
            if hasattr(peer, 'channel_id') and peer.channel_id != 0:
                return -peer.channel_id
            elif hasattr(peer, 'chat_id') and peer.chat_id != 0:
                return -peer.chat_id
            elif hasattr(peer, 'user_id') and peer.user_id != 0:
                return peer.user_id
        except Exception as e:
            self.log(f"Error getting chat ID from peer: {e}")
        return 0
        
    def _get_topic_id(self, params: Any) -> int:
        """Получение ID топика из параметров сообщения"""
        try:
            if hasattr(params, 'replyToTopMsg') and params.replyToTopMsg:
                if hasattr(params.replyToTopMsg, 'messageOwner'):
                    return getattr(params.replyToTopMsg.messageOwner, 'id', 0)
                return getattr(params.replyToTopMsg, 'id', 0)
        except Exception as e:
            self.log(f"Error getting topic ID: {e}")
        return 0
